<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VAPI Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #005a87; }
        .error { background: #ffe6e6; border-left: 4px solid #ff0000; }
        .success { background: #e6ffe6; border-left: 4px solid #00ff00; }
    </style>
</head>
<body>
    <div class="container">
        <h1>VAPI Debug Test</h1>
        <p>This is a standalone test to debug VAPI integration issues.</p>
        
        <div>
            <button onclick="testVapiConnection()">Test VAPI Connection</button>
            <button onclick="testWorkflowCall()">Test Workflow Call</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <div id="logs"></div>
    </div>

    <script src="https://unpkg.com/@vapi-ai/web@latest/dist/index.js"></script>
    <script>
        // Replace these with your actual values
        const VAPI_PUBLIC_KEY = '845105f5-71d0-4142-92c9-40d9073ae32a';
        const VAPI_WORKFLOW_ID = '55eabb2c-e0c9-4486-a991-faf4de56dfb8';
        
        let vapi;
        
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const logDiv = document.createElement('div');
            logDiv.className = `log ${type}`;
            logDiv.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logsDiv.appendChild(logDiv);
            logsDiv.scrollTop = logsDiv.scrollHeight;
            console.log(message);
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        function testVapiConnection() {
            try {
                log('Testing VAPI connection...');
                log(`Public Key: ${VAPI_PUBLIC_KEY.substring(0, 8)}...`);
                log(`Workflow ID: ${VAPI_WORKFLOW_ID.substring(0, 8)}...`);
                
                // Initialize VAPI
                vapi = new window.Vapi(VAPI_PUBLIC_KEY);
                log('✓ VAPI instance created successfully', 'success');
                
                // Set up event listeners
                vapi.on('call-start', () => log('✓ Call started', 'success'));
                vapi.on('call-end', () => log('✓ Call ended', 'success'));
                vapi.on('error', (error) => log(`✗ VAPI Error: ${error.message}`, 'error'));
                vapi.on('message', (message) => log(`Message: ${JSON.stringify(message)}`));
                
                log('✓ Event listeners set up', 'success');
                
            } catch (error) {
                log(`✗ Connection test failed: ${error.message}`, 'error');
            }
        }
        
        async function testWorkflowCall() {
            if (!vapi) {
                log('✗ Please test connection first', 'error');
                return;
            }
            
            try {
                log('Attempting workflow call...');
                
                // Test with minimal payload first
                await vapi.start(VAPI_WORKFLOW_ID);
                log('✓ Workflow call initiated successfully', 'success');
                
            } catch (error) {
                log(`✗ Workflow call failed: ${error.message}`, 'error');
                
                // Try to get more details about the error
                if (error.response) {
                    log(`HTTP Status: ${error.response.status}`, 'error');
                    log(`Status Text: ${error.response.statusText}`, 'error');
                    
                    try {
                        const responseText = await error.response.text();
                        log(`Response Body: ${responseText}`, 'error');
                    } catch (e) {
                        log('Could not read response body', 'error');
                    }
                }
            }
        }
        
        // Auto-test connection on load
        window.onload = function() {
            log('Page loaded. Ready for testing.');
            testVapiConnection();
        };
    </script>
</body>
</html>
