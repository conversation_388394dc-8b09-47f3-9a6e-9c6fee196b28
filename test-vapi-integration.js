// Simple Node.js script to test VAPI integration
const https = require('https');

// Test configuration
const VAPI_PUBLIC_KEY = '845105f5-71d0-4142-92c9-40d9073ae32a';
const VAPI_WORKFLOW_ID = '55eabb2c-e0c9-4486-a991-faf4de56dfb8';

console.log('🧪 Testing VAPI Integration...\n');

// Test 1: Verify API Key Format
console.log('Test 1: API Key Format');
console.log(`API Key: ${VAPI_PUBLIC_KEY.substring(0, 8)}...`);
console.log(`Format: ${VAPI_PUBLIC_KEY.match(/^[a-f0-9-]{36}$/) ? '✅ Valid UUID format' : '❌ Invalid format'}`);

// Test 2: Verify Workflow ID Format  
console.log('\nTest 2: Workflow ID Format');
console.log(`Workflow ID: ${VAPI_WORKFLOW_ID.substring(0, 8)}...`);
console.log(`Format: ${VAPI_WORKFLOW_ID.match(/^[a-f0-9-]{36}$/) ? '✅ Valid UUID format' : '❌ Invalid format'}`);

// Test 3: Test VAPI API Connectivity
console.log('\nTest 3: VAPI API Connectivity');

const testApiCall = () => {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            workflowId: VAPI_WORKFLOW_ID,
            workflowOverrides: {
                variableValues: {
                    username: 'Test User',
                    userid: 'test123'
                }
            }
        });

        const options = {
            hostname: 'api.vapi.ai',
            port: 443,
            path: '/call/web',
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${VAPI_PUBLIC_KEY}`,
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = https.request(options, (res) => {
            let data = '';
            
            res.on('data', (chunk) => {
                data += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    statusMessage: res.statusMessage,
                    headers: res.headers,
                    body: data
                });
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.write(postData);
        req.end();
    });
};

// Run the API test
testApiCall()
    .then(response => {
        console.log(`Status: ${response.statusCode} ${response.statusMessage}`);
        
        if (response.statusCode === 200 || response.statusCode === 201) {
            console.log('✅ API call successful!');
            console.log('Response:', JSON.parse(response.body));
        } else if (response.statusCode === 400) {
            console.log('❌ 400 Bad Request - Check request format');
            console.log('Response:', response.body);
        } else if (response.statusCode === 401) {
            console.log('❌ 401 Unauthorized - Check API key');
        } else if (response.statusCode === 404) {
            console.log('❌ 404 Not Found - Check workflow ID');
        } else {
            console.log(`❌ ${response.statusCode} ${response.statusMessage}`);
            console.log('Response:', response.body);
        }
    })
    .catch(error => {
        console.log('❌ Network error:', error.message);
    });

// Test 4: Environment Variables Check
console.log('\nTest 4: Environment Variables');
console.log(`NEXT_PUBLIC_VAPI_PUBLIC_KEY: ${process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY ? '✅ Set' : '❌ Not set'}`);
console.log(`NEXT_PUBLIC_VAPI_WORKFLOW_ID: ${process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID ? '✅ Set' : '❌ Not set'}`);

console.log('\n🔍 Test completed. Check results above.');
