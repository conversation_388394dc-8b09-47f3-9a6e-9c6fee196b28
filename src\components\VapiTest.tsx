"use client";
import { vapi } from "@/lib/vapi.sdk";
import { useEffect, useState } from "react";

const VapiTest = () => {
    const [status, setStatus] = useState('Not initialized');
    const [logs, setLogs] = useState<string[]>([]);

    const addLog = (message: string) => {
        console.log(message);
        setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
    };

    useEffect(() => {
        addLog('Initializing VAPI...');
        addLog(`Environment check:`);
        addLog(`- API Token: ${process.env.NEXT_PUBLIC_VAPI_API_TOKEN ? 'Set ✓' : 'Missing ✗'}`);
        addLog(`- Workflow ID: ${process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID ? 'Set ✓' : 'Missing ✗'}`);

        // Check if VAPI is properly initialized
        if (vapi) {
            addLog('VAPI instance created successfully ✓');
            setStatus('Initialized');
        } else {
            addLog('Failed to create VAPI instance ✗');
            setStatus('Failed');
            return;
        }

        // Set up event listeners
        const onCallStart = () => {
            addLog('Call started');
            setStatus('Call Active');
        };

        const onCallEnd = () => {
            addLog('Call ended');
            setStatus('Call Ended');
        };

        const onError = (error: Error) => {
            addLog(`Error: ${error.message}`);
            setStatus('Error');
        };

        const onMessage = (message: unknown) => {
            addLog(`Message received: ${JSON.stringify(message)}`);
        };

        vapi.on('call-start', onCallStart);
        vapi.on('call-end', onCallEnd);
        vapi.on('error', onError);
        vapi.on('message', onMessage);

        return () => {
            vapi.off('call-start', onCallStart);
            vapi.off('call-end', onCallEnd);
            vapi.off('error', onError);
            vapi.off('message', onMessage);
        };
    }, []);

    const testCall = async () => {
        try {
            addLog('Attempting to start call...');
            addLog(`Workflow ID: ${process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID}`);
            addLog(`API Token: ${process.env.NEXT_PUBLIC_VAPI_API_TOKEN ? 'Set' : 'Not set'}`);

            if (!process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID) {
                addLog('ERROR: Workflow ID not found');
                return;
            }

            await vapi.start(process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID, {
                variableValues: {
                    username: 'Test User',
                    userid: 'test123',
                }
            });

            addLog('Call start request sent successfully');
        } catch (error) {
            addLog(`Call failed: ${error instanceof Error ? error.message : String(error)}`);
            if (error instanceof Error && error.stack) {
                addLog(`Stack trace: ${error.stack}`);
            }
        }
    };

    const testAssistantCall = async () => {
        try {
            addLog('Attempting to start call with assistant configuration...');

            // Create a simple assistant configuration for testing
            const assistantConfig = {
                name: "Test Interviewer",
                firstMessage: "Hello! This is a test call. Can you hear me?",
                transcriber: {
                    provider: "deepgram",
                    model: "nova-2",
                    language: "en",
                },
                voice: {
                    provider: "11labs",
                    voiceId: "sarah",
                },
                model: {
                    provider: "openai",
                    model: "gpt-3.5-turbo",
                    messages: [
                        {
                            role: "system",
                            content: "You are a test assistant. Keep responses brief and friendly."
                        }
                    ]
                }
            };

            await vapi.start(assistantConfig);
            addLog('Assistant call start request sent successfully');
        } catch (error) {
            addLog(`Assistant call failed: ${error instanceof Error ? error.message : String(error)}`);
            if (error instanceof Error && error.stack) {
                addLog(`Stack trace: ${error.stack}`);
            }
        }
    };

    const stopCall = () => {
        try {
            addLog('Stopping call...');
            vapi.stop();
        } catch (error) {
            addLog(`Stop failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    };

    const testConnection = () => {
        addLog('Testing VAPI connection...');
        addLog(`VAPI object methods: ${Object.getOwnPropertyNames(Object.getPrototypeOf(vapi)).join(', ')}`);
        addLog(`VAPI instance type: ${typeof vapi}`);

        // Test if we can access VAPI properties
        try {
            addLog('Checking VAPI readiness...');
            // Most VAPI instances have some basic properties we can check
            if (vapi && typeof vapi.start === 'function') {
                addLog('✓ VAPI start method available');
            } else {
                addLog('✗ VAPI start method not available');
            }

            if (vapi && typeof vapi.stop === 'function') {
                addLog('✓ VAPI stop method available');
            } else {
                addLog('✗ VAPI stop method not available');
            }
        } catch (error) {
            addLog(`Connection test failed: ${error instanceof Error ? error.message : String(error)}`);
        }
    };

    return (
        <div className="p-6 max-w-2xl mx-auto">
            <h2 className="text-2xl font-bold mb-4">VAPI Test Component</h2>
            
            <div className="mb-4">
                <p><strong>Status:</strong> {status}</p>
            </div>

            <div className="flex gap-4 mb-6 flex-wrap">
                <button
                    onClick={testConnection}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
                >
                    Test Connection
                </button>
                <button
                    onClick={testCall}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
                >
                    Test Workflow Call
                </button>
                <button
                    onClick={testAssistantCall}
                    className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
                >
                    Test Assistant Call
                </button>
                <button
                    onClick={stopCall}
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
                >
                    Stop Call
                </button>
            </div>

            <div className="bg-gray-100 p-4 rounded max-h-96 overflow-y-auto">
                <h3 className="font-bold mb-2">Logs:</h3>
                {logs.map((log, index) => (
                    <div key={index} className="text-sm font-mono mb-1">
                        {log}
                    </div>
                ))}
            </div>
        </div>
    );
};

export default VapiTest;
