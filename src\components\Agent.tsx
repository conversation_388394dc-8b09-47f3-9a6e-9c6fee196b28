"use client";
import Image from "next/image";
import {cn} from "@/lib/utils";
import { vapi } from "@/lib/vapi.sdk";
import { useRouter } from "next/navigation";
import { useEffect, useState} from "react";

enum CallStatus {
    INACTIVE = 'INACTIVE',
    CONNECTING = 'CONNECTING',
    ACTIVE = 'ACTIVE',
    FINISHED = 'FINISHED',
}

interface SavedMessage {
    role: 'user' | 'system' | 'assistant';
    content: string;
}

const Agent = ({ userName, userId, type }: AgentProps) => {
    const router = useRouter();
    const [isSpeaking, setIsSpeaking] = useState(false);
    const [callStatus, setCallStatus] = useState<CallStatus>(CallStatus.INACTIVE);
    const [messages, setMessages] = useState<SavedMessage[]>([]);

useEffect(() => {
        const onCallStart = () => setCallStatus(CallStatus.ACTIVE);
        const onCallEnd = () => setCallStatus(CallStatus.FINISHED);

        const onMessage = (message: unknown) => {
            console.log('Received message:', message);
            if(message && typeof message === 'object' && 'type' in message &&
               message.type === 'transcript' && 'transcriptType' in message &&
               message.transcriptType === 'final' && 'role' in message && 'transcript' in message) {
                const newMessage = {
                    role: message.role as 'user' | 'system' | 'assistant',
                    content: message.transcript as string
                }

                setMessages((prev) => [...prev, newMessage]);
            }
        }

        const onSpeechStart = () => setIsSpeaking(true);
        const onSpeechEnd = () => setIsSpeaking(false);

        const onError = (error: Error) => console.log('Error', error);

        vapi.on('call-start', onCallStart);
        vapi.on('call-end', onCallEnd);
        vapi.on('message', onMessage);
        vapi.on('speech-start', onSpeechStart);
        vapi.on('speech-end', onSpeechEnd);
        vapi.on('error', onError);

        return () => {
            vapi.off('call-start', onCallStart);
            vapi.off('call-end', onCallEnd);
            vapi.off('message', onMessage);
            vapi.off('speech-start', onSpeechStart);
            vapi.off('speech-end', onSpeechEnd);
            vapi.off('error', onError)
        }
    }, [])

    useEffect(() => {
        if(callStatus === CallStatus.FINISHED) router.push('/');
    }, [messages, callStatus, type, userId, router]);

    const handleCall = async () => {
        try {
            console.log('Starting call with:', { userName, userId, workflowId: process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID });
            setCallStatus(CallStatus.CONNECTING);

            // Check if we have a workflow ID (primary approach)
            if (process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID) {
                console.log('Using Workflow approach');
                await vapi.start(process.env.NEXT_PUBLIC_VAPI_WORKFLOW_ID, {
                    variableValues: {
                        username: userName || 'User',
                        userid: userId || 'anonymous',
                    }
                });
            } else {
                console.log('Using inline assistant configuration as fallback');
                // Fallback to inline assistant configuration
                const assistantConfig = {
                    name: "Interview Assistant",
                    firstMessage: `Hello ${userName}! I'm your AI interviewer. Are you ready to begin the interview?`,
                    transcriber: {
                        provider: "deepgram" as const,
                        model: "nova-2" as const,
                        language: "en" as const,
                    },
                    voice: {
                        provider: "11labs" as const,
                        voiceId: "sarah" as const,
                    },
                    model: {
                        provider: "openai" as const,
                        model: "gpt-4" as const,
                        messages: [
                            {
                                role: "system" as const,
                                content: `You are a professional job interviewer. The candidate's name is ${userName}. Conduct a professional interview, ask relevant questions, and provide a good experience. Keep responses concise and engaging.`
                            }
                        ]
                    }
                };

                await vapi.start(assistantConfig);
            }

            console.log('Call start request sent successfully');
        } catch (error) {
            console.error('Error starting call:', error);

            // Enhanced error logging for debugging
            if (error instanceof Error) {
                console.error('Error name:', error.name);
                console.error('Error message:', error.message);
                console.error('Error stack:', error.stack);
            }

            // Check if it's a fetch error with response details
            if (error && typeof error === 'object' && 'response' in error) {
                const errorWithResponse = error as { response?: { status?: number; statusText?: string; text?: () => Promise<string> } };
                console.error('Response status:', errorWithResponse.response?.status);
                console.error('Response statusText:', errorWithResponse.response?.statusText);
                try {
                    const responseText = await errorWithResponse.response?.text?.();
                    console.error('Response body:', responseText);
                } catch {
                    console.error('Could not read response body');
                }
            }

            alert(`Failed to start call: ${error instanceof Error ? error.message : String(error)}`);
            setCallStatus(CallStatus.INACTIVE);
        }
    }

    const handleDisconnect = async () => {
        try {
            console.log('Disconnecting call...');
            setCallStatus(CallStatus.FINISHED);
            vapi.stop();
        } catch (error) {
            console.error('Error disconnecting call:', error);
        }
    }

    const latestMessage = messages[messages.length - 1]?.content;

    return (
        <>
        <div className="call-view">
            <div className="card-interviewer">
                <div className="avatar">
                    <Image src="/ai-avatar.png" alt="vapi" width={65} height={54} className="object-cover" />
                    {isSpeaking && <span className="animate-speak" />}
                </div>
                <h3>AI Interviewer</h3>
            </div>

            <div className="card-border">
                <div className="card-content">
                    <Image src="/xENfQsk9_400x400.jpg" alt="user avatar" width={540} height={540} className="rounded-full object-cover size-[120px]" />
                    <h3>{userName}</h3>
                </div>
            </div>
        </div>
            {messages.length > 0 && (
                <div className="transcript-border">
                    <div className="transcript">
                        <p key={latestMessage} className={cn('transition-opacity duration-500 opacity-0', 'animate-fadeIn opacity-100')}>
                            {latestMessage}
                        </p>
                    </div>
                </div>
            )}

            <div className="w-full flex justify-center">
                {callStatus !== CallStatus.ACTIVE ? (
                    <button className="relative btn-call" onClick={handleCall}>
                        <span className={cn('absolute animate-ping rounded-full opacity-75', callStatus !== CallStatus.CONNECTING && 'hidden')}
                             />

                            <span>
                                {callStatus === CallStatus.INACTIVE || callStatus === CallStatus.FINISHED ? 'Call' : '. . . '}
                            </span>
                    </button>
                ) : (
                    <button className="btn-disconnect" onClick={handleDisconnect}>
                        End
                    </button>
                )}
            </div>
        </>
    )
}
export default Agent